﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public class LotesNodrizasRepo : Repository<Lotesnodrizas, APQLitalsaContext>, ILotesNodrizasRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public LotesNodrizasRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }

    public async Task<Lotesnodrizas?> GetUltimoLoteNodrizaByIdProducto(int numNodriza,
        int idProducto, bool asNoTracking, CancellationToken cancellationToken)
    {
        return await GetLastOrDefault(asNoTracking, cancellationToken,
                   i =>
                       i.Nodriza.HasValue && i.Nodriza.Value == numNodriza
                                          && i.Idproducto.HasValue
                                          && i.Idproducto.Value == idProducto,
                   i => i.Id) ??
               new Lotesnodrizas();
    }

    public async Task<List<Lotesnodrizas>> GetLotesNodrizaByIdProducto(int idProducto, bool asNoTracking,
        CancellationToken cancellationToken)
    {
        return await GetList(asNoTracking, cancellationToken,
            i => i.Idproducto.HasValue
                 && i.Idproducto.Value == idProducto);
    }
}