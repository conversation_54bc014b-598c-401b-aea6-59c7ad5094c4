namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public class TablaPesosEnvasesRepo : Repository<TablaPesosEnvases, APQLitalsaContext>, ITablaPesosEnvasesRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public TablaPesosEnvasesRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }

    public async Task<TablaPesosEnvases?> GetPesoEnvasePorId(int id, bool asNoTracking, CancellationToken cancellationToken)
    {
        return await GetFirstOrDefault(asNoTracking, cancellationToken, x => x.Id == id);
    }
}
