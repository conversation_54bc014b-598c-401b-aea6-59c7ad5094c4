﻿namespace GestionAPQ_BLZ.Repositories.DatoLita01;

using Base;
using Base.DatoLita01;
using GestionAQP_BLZ.Server.Data.DbContexts.DatoLita01;
using GestionAQP_BLZ.Server.Data.Entities.DatoLita01;
using Microsoft.EntityFrameworkCore;

public class AEntdiaRepo : Repository<AEntdia, DatoLita01Context>, IAEntdiaRepo
{
    public AEntdiaRepo(DatoLita01Context dbContext) : base(dbContext)
    {
    }

    public IQueryable<AEntdia> GetIQueryableEntradas(string? idProducto, bool asNoTracking)
    {
        var query = asNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();
        if (!string.IsNullOrEmpty(idProducto))
            query = query.Where(i => i.Codigo.Equals(idProducto));

        return query;
    }
}