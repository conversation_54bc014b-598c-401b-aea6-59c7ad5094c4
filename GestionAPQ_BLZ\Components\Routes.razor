﻿@inject IBlazrRenderStateService RenderState

@if (RenderState.IsPreRender)
{
	<Splash/>
	return;
}

<Router AppAssembly="@typeof(Program).Assembly" AdditionalAssemblies="new[] { typeof(Client._Imports).Assembly }">
	<Found Context="routeData">
		<RouteView RouteData="@routeData" DefaultLayout="typeof(MainLayout)"/>
		<FocusOnNavigate RouteData="@routeData" Selector="h1"/>
	</Found>
	<NotFound>
		<LayoutView Layout="@typeof(MainLayout)">
			<p>Sorry, there's nothing at this address.</p>
		</LayoutView>
	</NotFound>
</Router>