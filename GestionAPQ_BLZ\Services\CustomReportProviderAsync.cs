using DevExpress.XtraReports.Services;
using DevExpress.XtraReports.UI;
using GestionAQP_BLZ.Server.Reports;
using System.Web;

namespace GestionAPQ_BLZ.Services;

public class CustomReportProviderAsync : IReportProviderAsync
{
    public async Task<XtraReport> GetReportAsync(string id, ReportProviderContext context)
    {
        // Parse the string with the report name and parameter values.
        string[] parts = id.Split('?');
        string reportName = parts[0];
        string parametersQueryString = parts.Length > 1 ? parts[1] : string.Empty;

        // Create a report instance.
        XtraReport report;

        if (reportName == "ReporteEtiqueta")
        {
            report = new ReporteEtiqueta();
        }
        else
        {
            throw new DevExpress.XtraReports.Web.ClientControls.FaultException(
                string.Format("Could not find report '{0}'.", reportName)
            );
        }

        // Apply the parameter values to the report.
        if (!string.IsNullOrEmpty(parametersQueryString))
        {
            var parameters = HttpUtility.ParseQueryString(parametersQueryString);

            foreach (string parameterName in parameters.AllKeys)
            {
                if (report.Parameters[parameterName] != null)
                {
                    var paramValue = parameters.Get(parameterName);
                    report.Parameters[parameterName].Value = Convert.ChangeType(
                        paramValue, report.Parameters[parameterName].Type);
                }
            }
            foreach (var parameter in report.Parameters)
                parameter.Visible = false;
        }
        return await Task.FromResult(report);
    }
}
