namespace GestionAPQ_BLZ.MediaTR.Command;

using Common.ResponseModels.ResponseModels;
using MediatR;
using Repositories.Base.APQLitalsa;

public record DeletePesoEnvaseCommand(int Id) : IRequest<SingleResult<int>>;

public class DeletePesoEnvaseCommandHandler : IRequestHandler<DeletePesoEnvaseCommand, SingleResult<int>>
{
    private readonly ITablaPesosEnvasesRepo _tablaPesosEnvasesRepo;

    public DeletePesoEnvaseCommandHandler(ITablaPesosEnvasesRepo tablaPesosEnvasesRepo)
    {
        _tablaPesosEnvasesRepo = tablaPesosEnvasesRepo;
    }

    public async Task<SingleResult<int>> Handle(DeletePesoEnvaseCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Errors = [],
            Data = 0
        };

        var pesoEnvase = await _tablaPesosEnvasesRepo.GetPesoEnvasePorId(
            request.Id,
            false,
            cancellationToken);

        if (pesoEnvase == null)
        {
            result.Errors.Add("No se ha encontrado el peso de envase en la base de datos.");
            return result;
        }

        var resultDb = await _tablaPesosEnvasesRepo.DeleteEntity(cancellationToken, pesoEnvase);
        result.Data = resultDb;

        return result;
    }
}
