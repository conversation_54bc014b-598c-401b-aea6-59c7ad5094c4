﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public class TablaOperariosRepo : Repository<TablaOperarios, APQLitalsaContext>, ITablaOperariosRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public TablaOperariosRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }
}