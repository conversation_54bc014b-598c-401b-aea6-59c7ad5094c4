﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.options\8.0.2\buildTransitive\net6.0\Microsoft.Extensions.Options.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.options\8.0.2\buildTransitive\net6.0\Microsoft.Extensions.Options.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.binder\8.0.2\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.binder\8.0.2\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.5\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\9.0.5\buildTransitive\net8.0\Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.aspnetcore.components.webassembly.server\8.0.15\build\Microsoft.AspNetCore.Components.WebAssembly.Server.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.aspnetcore.components.webassembly.server\8.0.15\build\Microsoft.AspNetCore.Components.WebAssembly.Server.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.aspnetcore.components.analyzers\8.0.15\buildTransitive\netstandard2.0\Microsoft.AspNetCore.Components.Analyzers.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.aspnetcore.components.analyzers\8.0.15\buildTransitive\netstandard2.0\Microsoft.AspNetCore.Components.Analyzers.targets')" />
    <Import Project="C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.reporting.core\24.2.8\buildTransitive\DevExpress.Reporting.Core.targets" Condition="Exists('C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.reporting.core\24.2.8\buildTransitive\DevExpress.Reporting.Core.targets')" />
    <Import Project="C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.blazor\24.2.8\buildTransitive\net5.0\DevExpress.Blazor.targets" Condition="Exists('C:\Program Files\DevExpress 24.2\Components\Offline Packages\devexpress.blazor\24.2.8\buildTransitive\net5.0\DevExpress.Blazor.targets')" />
  </ImportGroup>
</Project>