﻿<DxLoadingPanel @bind-Visible="_visibleReal"
                PositionTarget="@PositionTarget"
                ApplyBackgroundShading="@ApplyBackgroundShading.Value"
                IsContentVisible="@IsContentVisible.Value"
                Text="&nbsp;&nbsp;Cargando..."
                TextAlignment="LoadingPanelTextAlignment.Right"
                IndicatorAnimationType="WaitIndicatorAnimationType.Flip"
                IndicatorAreaVisible="true">
	<IndicatorTemplate>
		<div class="me-4" style="align-items: center; display: flex; height: 32px; justify-content: center; width: 32px;">
			<img src="../images/logo-litalsa.png" class="h-100"/>
		</div>
	</IndicatorTemplate>
</DxLoadingPanel>

@code {
	[Parameter] public bool Visible { get; set; }
	[Parameter] public string? PositionTarget { get; set; }
	[Parameter] public EventCallback<bool> VisibleChanged { get; set; }
	[Parameter] public bool? ApplyBackgroundShading { get; set; }
	[Parameter] public bool? IsContentVisible { get; set; }

	private bool _visibleReal { get; set; }

	protected override async Task OnParametersSetAsync()
	{
		PositionTarget ??= "body";
		ApplyBackgroundShading ??= true;
		IsContentVisible ??= true;

		if ((Visible && !_visibleReal) || (!Visible && _visibleReal))
		{
			// si se pone como positiontarget un popup (o algo así), a veces peta por temas de renderizado del dom
			// ya que puede que se meta como positiontarget elementos que aun no existen en el dom
			await Task.Delay(25);
			_visibleReal = Visible;
		}

		await base.OnParametersSetAsync();
	}

	// protected override async Task OnAfterRenderAsync(bool firstRender)
	// {
	// 	if (Visible && !_visibleReal)
	// 	{
	// 		// Fallback: si aún no se ha mostrado, forzar la visualización
	// 		await Task.Delay(100);
	// 		_visibleReal = true;
	// 		StateHasChanged();
	// 	}

	// 	await base.OnAfterRenderAsync(firstRender);
	// }
}