﻿namespace GestionAPQ_BLZ.Repositories.DatoLita01;

using Base;
using Base.DatoLita01;
using GestionAQP_BLZ.Server.Data.DbContexts.DatoLita01;
using GestionAQP_BLZ.Server.Data.Entities.DatoLita01;
using Microsoft.EntityFrameworkCore;

public class ALotessRepo : Repository<ALotess, DatoLita01Context>, IALotessRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public ALotessRepo(DatoLita01Context dbContext) : base(dbContext)
    {
    }

    public IQueryable<ALotess> GetIQueryableLotesConStock(string? idProducto, bool asNoTracking)
    {
        var query = asNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();
        query = query.Where(i => i.Stock.HasValue
                                 && i.Stock.Value > 0);

        if (!string.IsNullOrEmpty(idProducto))
            query = query.Where(i => i.Codigo.Equals(idProducto));

        return query;
    }
}