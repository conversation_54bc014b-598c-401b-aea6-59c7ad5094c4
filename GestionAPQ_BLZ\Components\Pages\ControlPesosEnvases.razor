@page "/ControlPesosEnvases"

@inject IMediator Mediator
@inject ICustomToastService ToastService
@inject ILocalStorageService LocalStorageService

<style>
	.popup-pesos-envases {
		width: 850px !important;
		max-width: 95vw !important;
	}

	.dxbl-modal-body {
		padding: 0 !important;
	}
</style>

<PageTitle>Control Pesos Envases</PageTitle>

<CustomLoadingPanel @bind-Visible="Cargando" />
<DxDialogProvider />

<div class="d-flex flex-column h-100 overflow-x-hidden">
	<div class="container">
		<div class="d-flex align-items-center justify-content-between p-3 bg-light border rounded shadow-sm flex-shrink-0">
			<div class="d-flex align-items-center flex-grow-1">
				<h1 class="h5 fw-bold mb-0 text-primary me-2 flex-shrink-0">
					<i class="bi bi-speedometer me-1"></i>
					CONTROL PESOS ENVASES
				</h1>
			</div>
		</div>
	</div>
	<div class="flex-grow-1 overflow-hidden pt-3 h-100">
		<div class="w-100 h-100">
			<DxGrid @ref="GridPesosEnvases"
					Data="@_listaPesosEnvases"
					SizeMode="SizeMode.Small"
					CssClass="h-100 w-100"
					PageSize="50"
					SelectionMode="GridSelectionMode.Single"
					AllowSelectRowByClick="true"
					AllowSort="true"
					ShowFilterRow="true"
					EditMode="GridEditMode.PopupEditForm"
					PopupEditFormHeaderText="@TituloPopup"
					PopupEditFormCssClass="popup-pesos-envases"
					KeyFieldName="@nameof(TablaPesosEnvasesDTO.Id)"
					EditModelSaving="@GridPesosEnvases_EditModelSaving"
					CustomizeEditModel="@GridPesosEnvases_CustomizeEditModel"
					UnboundColumnData="@GridPesosEnvases_UnboundColumnData"
					EditFormButtonsVisible="false">
				<Columns>
					<CustomDxGridCommandColumn TooltipBtnEditar="Editar Peso Envase"
											   TooltipBtnEliminar="Eliminar Peso Envase"
											   TooltipBtnNuevo="Nuevo Peso Envase"
											   NewButtonVisible="true"
											   EditButtonVisible="true"
											   DeleteButtonVisible="false"
											   OnDeleteButtonClicked="@(async (e) => await GridPesosEnvases_HandleDeleteButtonClicked(e))" />
					<DxGridDataColumn Caption="Etiqueta" Width="60" AllowSort="false" AllowGroup="false">
						<CellDisplayTemplate>
							@{
								var pesoEnvase = (TablaPesosEnvasesDTO)context.DataItem;
							}
							<DxButton IconCssClass="bi bi-printer"
									  RenderStyle="ButtonRenderStyle.Info"
									  RenderStyleMode="ButtonRenderStyleMode.Contained"
									  CssClass="btn-sm"
									  Click="@(async () => await MostrarReporte(pesoEnvase.Numenvase ?? 0))" />
						</CellDisplayTemplate>
					</DxGridDataColumn>

					<DxGridDataColumn FieldName="@nameof(TablaPesosEnvasesDTO.Numenvase)"
									  Caption="Nº Envase"
									  Width="90"
									  DisplayFormat="F0"
									  TextAlignment="GridTextAlignment.Center" 
									  SortIndex="0"
									  SortOrder="GridColumnSortOrder.Descending"/>
					<DxGridDataColumn FieldName="@nameof(TablaPesosEnvasesDTO.Idproducto)"
									  Caption="Producto"
									  Width="90"
									  DisplayFormat="F0"
									  TextAlignment="GridTextAlignment.Center" />
					<DxGridDataColumn FieldName="@nameof(TablaPesosEnvasesDTO.Lote)"
									  Caption="Lote"
									  Width="100" />
					<DxGridDataColumn FieldName="@nameof(TablaPesosEnvasesDTO.Ubicacion)"
									  Caption="Ubicación"
									  Width="100"
									  TextAlignment="GridTextAlignment.Center" />
					<DxGridDataColumn FieldName="@nameof(TablaPesosEnvasesDTO.Fecha)"
									  Caption="Fecha Entrada"
									  Width="110"
									  DisplayFormat="{0:dd/MM/yyyy HH:mm}"
									  SortIndex="0"
									  SortOrder="GridColumnSortOrder.Descending">
					</DxGridDataColumn>
					<DxGridDataColumn FieldName="@nameof(TablaPesosEnvasesDTO.FechaCaducidad)"
									  Caption="Fecha Caducidad"
									  Width="110"
									  DisplayFormat="{0:dd/MM/yyyy}">
					</DxGridDataColumn>
					<DxGridDataColumn FieldName="@nameof(TablaPesosEnvasesDTO.Observaciones)"
									  Caption="Observaciones"
									  MinWidth="200" />
					<DxGridDataColumn FieldName="@nameof(TablaPesosEnvasesDTO.RealizadoPor)"
									  Caption="Realizado Por"
									  Width="130" />
					<DxGridDataColumn FieldName="@nameof(TablaPesosEnvasesDTO.Pesoteoricoentrada)"
									  Caption="Peso Neto Entrada (Etiqueta)"
									  Width="95"
									  TextAlignment="GridTextAlignment.Center" />
					<DxGridDataColumn FieldName="@nameof(TablaPesosEnvasesDTO.Pesorealentrada)"
									  Caption="Peso Bruto Entrada (Medido)"
									  Width="95"
									  TextAlignment="GridTextAlignment.Center" />
					<DxGridDataColumn FieldName="@nameof(TablaPesosEnvasesDTO.StockActual)"
									  Caption="Peso Real"
									  Width="100"
									  TextAlignment="GridTextAlignment.Center"
									  DisplayFormat="F2" />
					<DxGridDataColumn FieldName="Diferencia"
									  Caption="Sobrepeso en entrada"
									  Width="85"
									  UnboundType="GridUnboundColumnType.Integer"
									  UnboundExpression="@($"[{nameof(TablaPesosEnvasesDTO.Pesorealentrada)}] - [{nameof(TablaPesosEnvasesDTO.Pesoteoricoentrada)}] - [{nameof(TablaPesosEnvasesDTO.Pesoenvase)}]")"
									  TextAlignment="GridTextAlignment.Center" />
					<DxGridDataColumn FieldName="@nameof(TablaPesosEnvasesDTO.Pesoenvase)"
									  Caption="Peso Entrada Envase (en etiqueta)"
									  Width="95"
									  TextAlignment="GridTextAlignment.Center" />
					<DxGridDataColumn FieldName="@nameof(TablaPesosEnvasesDTO.Pesovacio)"
									  Caption="Peso Salida en vacío (medido)"
									  Width="95"
									  TextAlignment="GridTextAlignment.Center" />
					<DxGridDataColumn FieldName="BarnizTirado"
									  Caption="Barniz Tirado"
									  Width="85"
									  UnboundType="GridUnboundColumnType.Integer"
									  UnboundExpression="@($"[{nameof(TablaPesosEnvasesDTO.Pesovacio)}] - [{nameof(TablaPesosEnvasesDTO.Pesoenvase)}]")"
									  TextAlignment="GridTextAlignment.Center" />
					<DxGridDataColumn FieldName="@nameof(TablaPesosEnvasesDTO.FechaFin)"
									  Caption="Fecha Salida (al introducir peso)"
									  Width="110"
									  DisplayFormat="{0:dd/MM/yyyy HH:mm}">
					</DxGridDataColumn>
				</Columns>
				<EditFormTemplate Context="editFormContext">
					@{
						var editModel = (TablaPesosEnvasesDTO)editFormContext.EditModel;
					}
					<div class="">
						<div class="p-3">
							<DxFormLayout>
								<DxFormLayoutGroup Caption="Información Envase" ColSpanMd="12">
									<DxFormLayoutItem Caption="Nº Envase:" ColSpanMd="2" CaptionPosition="CaptionPosition.Vertical">
										<DxTextBox @bind-Text="@NumEnvaseDisplay" ReadOnly="true" CssClass="w-100" />
									</DxFormLayoutItem>
									<DxFormLayoutItem Caption="Producto:" ColSpanMd="7" CaptionPosition="CaptionPosition.Vertical">
										<DxComboBox Data="@_ddProductos"
													@bind-Value="@editModel.Idproducto"
													ValueFieldName="@($"{nameof(DetalleBarnizDTO.Articulo)}.{nameof(AArticuDTO.Codigo)}")"
													TextFieldName="@($"{nameof(DetalleBarnizDTO.Articulo)}.{nameof(AArticuDTO.Descrip)}")"
													SearchFilterCondition="ListSearchFilterCondition.Contains"
													SearchMode="ListSearchMode.AutoSearch"
													ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
													CssClass="w-100"
													EditFormat="{0}, {1}">
											<DxListEditorColumn FieldName="@($"{nameof(DetalleBarnizDTO.Articulo)}.{nameof(AArticuDTO.Codigo)}")" Caption="Producto" />
											<DxListEditorColumn FieldName="@($"{nameof(DetalleBarnizDTO.Articulo)}.{nameof(AArticuDTO.Descrip)}")" Caption="Descripción" />
										</DxComboBox>
									</DxFormLayoutItem>
									<DxFormLayoutItem Caption="Lote:" ColSpanMd="3" CaptionPosition="CaptionPosition.Vertical">
										@editFormContext.GetEditor(nameof(TablaPesosEnvasesDTO.Lote))
									</DxFormLayoutItem>
									<DxFormLayoutItem Caption="Ubicación:" ColSpanMd="4" CaptionPosition="CaptionPosition.Vertical">
										@editFormContext.GetEditor(nameof(TablaPesosEnvasesDTO.Ubicacion))
									</DxFormLayoutItem>
									<DxFormLayoutItem Caption="Fecha Entrada:" ColSpanMd="4" CaptionPosition="CaptionPosition.Vertical">
										<DxDateEdit @bind-Date="@editModel.Fecha"
													TimeSectionVisible="true"
													DisplayFormat="{0:dd/MM/yyyy HH:mm:ss}"
													CssClass="w-100"
													@ondblclick="@(() => SetFechaAuto(() => editModel.Fecha = DateTime.Now))" />
									</DxFormLayoutItem>
									<DxFormLayoutItem Caption="Fecha Caducidad:" ColSpanMd="4" CaptionPosition="CaptionPosition.Vertical">
										<DxDateEdit @bind-Date="@editModel.FechaCaducidad"
													TimeSectionVisible="true"
													DisplayFormat="{0:dd/MM/yyyy HH:mm:ss}"
													CssClass="w-100"
													@ondblclick="@(() => SetFechaAuto(() => editModel.FechaCaducidad = DateTime.Now))" />
									</DxFormLayoutItem>
									<DxFormLayoutItem Caption="Observaciones:" ColSpanMd="12" CaptionPosition="CaptionPosition.Vertical">
										@editFormContext.GetEditor(nameof(TablaPesosEnvasesDTO.Observaciones))
									</DxFormLayoutItem>
									<DxFormLayoutItem Caption="Realizado Por:" ColSpanMd="6" CaptionPosition="CaptionPosition.Vertical">
										<DxTextBox @bind-Text="@editModel.RealizadoPor"
												   ReadOnly="true"
												   CssClass="w-100" />
									</DxFormLayoutItem>
									<DxFormLayoutItem Caption="Operario Actual:" ColSpanMd="6" CaptionPosition="CaptionPosition.Vertical">
										<DxComboBox Data="@_ddOperarios"
													@bind-Value="@OperarioActualSelected"
													NullText="Selecciona un operario..."
													ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
													ValueFieldName="@nameof(TablaOperariosDTO.Id)"
													TextFieldName="@nameof(TablaOperariosDTO.Operario)"
													SearchFilterCondition="ListSearchFilterCondition.Contains"
													SearchMode="ListSearchMode.AutoSearch"
													CssClass="w-100" />
									</DxFormLayoutItem>
								</DxFormLayoutGroup>

								<DxFormLayoutGroup Caption="Mediciones de Peso" ColSpanMd="12">
									<DxFormLayoutItem Caption="Peso Neto Entrada (Etiqueta):" ColSpanMd="3" CaptionPosition="CaptionPosition.Vertical">
										<DxSpinEdit @bind-Value="@editModel.Pesoteoricoentrada"
													@bind-Value:after="@(() => ActualizarCamposCalculados(editModel))"
													CssClass="w-100" />
									</DxFormLayoutItem>
									<DxFormLayoutItem Caption="Peso Bruto Entrada (Medido):" ColSpanMd="3" CaptionPosition="CaptionPosition.Vertical">
										<DxSpinEdit @bind-Value="@editModel.Pesorealentrada"
													@bind-Value:after="@(() => ActualizarCamposCalculados(editModel))"
													CssClass="w-100" />
									</DxFormLayoutItem>
									<DxFormLayoutItem Caption="Peso Real:" ColSpanMd="2" CaptionPosition="CaptionPosition.Vertical">
										@editFormContext.GetEditor(nameof(TablaPesosEnvasesDTO.StockActual))
									</DxFormLayoutItem>
									<DxFormLayoutItem Caption="Sobrepeso en entrada:" ColSpanMd="4" CaptionPosition="CaptionPosition.Vertical">
										<DxSpinEdit @bind-Value="@Diferencia" ReadOnly="true" CssClass="w-100" />
									</DxFormLayoutItem>

									<DxFormLayoutItem Caption="Peso Entrada Envase (en etiqueta):" ColSpanMd="4" CaptionPosition="CaptionPosition.Vertical">
										<DxSpinEdit @bind-Value="@editModel.Pesoenvase"
													@bind-Value:after="@(() => ActualizarCamposCalculados(editModel))"
													CssClass="w-100" />
									</DxFormLayoutItem>
									<DxFormLayoutItem Caption="Peso Salida en vacío (medido):" ColSpanMd="4" CaptionPosition="CaptionPosition.Vertical">
										<DxSpinEdit @bind-Value="@editModel.Pesovacio"
													@bind-Value:after="@(() => ActualizarCamposCalculados(editModel))"
													CssClass="w-100" />
									</DxFormLayoutItem>
									<DxFormLayoutItem Caption="Barniz Tirado:" ColSpanMd="4" CaptionPosition="CaptionPosition.Vertical">
										<DxSpinEdit @bind-Value="@BarnizTirado" ReadOnly="true" CssClass="w-100" />
									</DxFormLayoutItem>

									<DxFormLayoutItem Caption="" ColSpanMd="8" CaptionPosition="CaptionPosition.Vertical">
									</DxFormLayoutItem>
									<DxFormLayoutItem Caption="Fecha Salida (al introducir peso):" ColSpanMd="4" CaptionPosition="CaptionPosition.Vertical">
										<DxDateEdit @bind-Date="@editModel.FechaFin"
													TimeSectionVisible="true"
													DisplayFormat="{0:dd/MM/yyyy HH:mm:ss}"
													CssClass="w-100"
													@ondblclick="@(() => SetFechaAuto(() => editModel.FechaFin = DateTime.Now))" />
									</DxFormLayoutItem>
								</DxFormLayoutGroup>
							</DxFormLayout>
						</div>
						<div class="d-flex justify-content-end w-100 border-top p-3 bg-light">
							<div>
								<DxButton SubmitFormOnClick="true"
										  Text="Grabar"
										  RenderStyle="ButtonRenderStyle.Success"
										  RenderStyleMode="ButtonRenderStyleMode.Contained"
										  CssClass="px-3"
										  IconCssClass="bi bi-check-circle me-1" />
								<DxButton RenderStyle="ButtonRenderStyle.Secondary"
										  RenderStyleMode="ButtonRenderStyleMode.Contained"
										  IconCssClass="bi bi-x-circle me-1"
										  CssClass="px-3"
										  Text="Cancelar"
										  Click="@(() => GridPesosEnvases.CancelEditAsync())" />
							</div>
						</div>
					</div>
				</EditFormTemplate>
			</DxGrid>
		</div>
	</div>
</div>

<CustomDocumentViewer @bind-Visible="MostrarVentanaReporte"
					  NombreReporte="@ParameterNombreReport"
					  TituloVentana="Reporte Etiqueta" />





@code
{
	private bool Cargando { get; set; }
	private TablaOperariosDTO? OperarioActualSelected { get; set; }
	private double? Diferencia { get; set; }
	private double? BarnizTirado { get; set; }
	private string TituloPopup { get; set; } = "Peso de Envase";
	private string NumEnvaseDisplay { get; set; } = "";
	private DxGrid GridPesosEnvases { get; set; }
	private bool MostrarVentanaReporte { get; set; }
	private int NumEnvaseSeleccionado { get; set; }
	private string ParameterNombreReport { get; set; } = string.Empty;


	private List<TablaPesosEnvasesDTO> _listaPesosEnvases = [];
	private List<TablaOperariosDTO> _ddOperarios = [];
	private List<DetalleBarnizDTO> _ddProductos = [];

	protected override async Task OnInitializedAsync()
	{
		await EjecutarConCarga(async () => { await CargarDatos(); });
	}

	private async Task CargarDatos()
	{
		// ejecutamos querys
		var resultPesosEnvases = await Mediator.Send(new GetAllEntitiesQuery<TablaPesosEnvases, TablaPesosEnvasesDTO>());
		var resultOperarios = await Mediator.Send(new GetAllEntitiesQuery<TablaOperarios, TablaOperariosDTO>());
		var resultBarnices = await Mediator.Send(new GetDetallesBarnicesQuery(null, false));

		// comprobamos si hay errores y mostramos dado el caso
		var listaErrores = resultPesosEnvases.Errors.Concat(resultOperarios.Errors).Concat(resultBarnices.Errors).ToArray();
		if (listaErrores.Any())
		{
			ToastService.MostrarError(listaErrores.First());
			return;
		}

		// asignamos datos
		_listaPesosEnvases = resultPesosEnvases.Data;
		_ddOperarios = resultOperarios.Data;
		_ddProductos = resultBarnices.Data;
	}

	private async Task GridPesosEnvases_CustomizeEditModel(GridCustomizeEditModelEventArgs e)
	{
		var editModel = (TablaPesosEnvasesDTO)e.EditModel;

		if (e.IsNew)
		{
			editModel.Fecha = DateTime.Now;
			editModel.FechaFin = DateTime.Now;
			editModel.Numenvase = GetNumEnvase();
			TituloPopup = "Nuevo Peso de Envase";
		}
		else
		{
			TituloPopup = "Editar Peso de Envase";
		}

		NumEnvaseDisplay = editModel.Numenvase?.ToString() ?? "";
		await CargarOperarioActualDesdeLocalStorage();
		ActualizarCamposCalculados(editModel);
	}

	private async Task GridPesosEnvases_EditModelSaving(GridEditModelSavingEventArgs e)
	{
		var pesoEnvase = (TablaPesosEnvasesDTO)e.EditModel;
		pesoEnvase.RealizadoPor = OperarioActualSelected?.Operario ?? string.Empty;

		await EjecutarConCarga(async () =>
		{
			var result = await Mediator.Send(new GrabarPesoEnvaseCommand(pesoEnvase));

			if (result.Errors.Any())
			{
				ToastService.MostrarError(result.Errors.First());
				e.Cancel = true;
				return;
			}

			ToastService.MostrarOk($"Peso de envase {(e.IsNew ? "creado" : "actualizado")} correctamente");
			if (pesoEnvase.Pesovacio.HasValue && pesoEnvase.Pesovacio.Value != 0
			    && pesoEnvase.Pesoenvase.HasValue && pesoEnvase.Pesoenvase.Value != 0
				&& BarnizTirado > 4)
				ToastService.MostrarWarning($"Peso de barniz tirado superior a 4 kg. Avisar a Control de Materias Primas.");
			await CargarDatos();
		});
	}

	private async Task GridPesosEnvases_HandleDeleteButtonClicked(GridCommandColumnCellDisplayTemplateContext context)
	{
		var pesoEnvase = (TablaPesosEnvasesDTO)context.DataItem;

		await EjecutarConCarga(async () =>
		{
			var result = await Mediator.Send(new DeletePesoEnvaseCommand(pesoEnvase.Id));

			if (result.Errors.Any())
			{
				ToastService.MostrarError(result.Errors.First());
				return;
			}

			ToastService.MostrarOk("Peso de envase eliminado correctamente");
			await CargarDatos();
		});
	}

	private void GridPesosEnvases_UnboundColumnData(GridUnboundColumnDataEventArgs e)
	{
		if (e.FieldName == "Diferencia")
		{
			var pesoReal = Convert.ToInt32(e.GetRowValue(nameof(TablaPesosEnvasesDTO.Pesorealentrada)) ?? 0);
			var pesoTeorico = Convert.ToInt32(e.GetRowValue(nameof(TablaPesosEnvasesDTO.Pesoteoricoentrada)) ?? 0);
			var pesoEnvase = Convert.ToInt32(e.GetRowValue(nameof(TablaPesosEnvasesDTO.Pesoenvase)) ?? 0);
			e.Value = pesoReal - pesoTeorico - pesoEnvase;
		}
		else if (e.FieldName == "BarnizTirado")
		{
			var pesoVacio = Convert.ToInt32(e.GetRowValue(nameof(TablaPesosEnvasesDTO.Pesovacio)) ?? 0);
			var pesoEnvase = Convert.ToInt32(e.GetRowValue(nameof(TablaPesosEnvasesDTO.Pesoenvase)) ?? 0);
			e.Value = pesoVacio - pesoEnvase;
		}
	}

	private double CalcularDiferencia(TablaPesosEnvasesDTO modelo)
	{
		var pesoReal = modelo.Pesorealentrada ?? 0;
		var pesoTeorico = modelo.Pesoteoricoentrada ?? 0;
		var pesoEnvase = modelo.Pesoenvase ?? 0;
		return (pesoReal - pesoTeorico - pesoEnvase);
	}

	private double CalcularBarnizTirado(TablaPesosEnvasesDTO modelo)
	{
		var pesoVacio = modelo.Pesovacio ?? 0;
		var pesoEnvase = modelo.Pesoenvase ?? 0;
		return (pesoVacio - pesoEnvase);
	}

	private void ActualizarCamposCalculados(TablaPesosEnvasesDTO modelo)
	{
		Diferencia = CalcularDiferencia(modelo);
		BarnizTirado = CalcularBarnizTirado(modelo);
	}

	private async Task CargarOperarioActualDesdeLocalStorage()
	{
		var operarioActual = await LocalStorageService.GetItemAsync<TablaOperariosDTO>(EnumKeysLocalStorage.Operario);
		if (operarioActual is not null && _ddOperarios.Any())
			OperarioActualSelected = _ddOperarios.FirstOrDefault(i => i.Id == operarioActual.Id && i.Operario == operarioActual.Operario);
		await InvokeAsync(() => StateHasChanged());
	}

	private async Task SetFechaAuto(Action setDateTime)
	{
		setDateTime();
		await InvokeAsync(() => StateHasChanged());
	}

	private int GetNumEnvase()
	{
		var maxNumero = _listaPesosEnvases.Any() ? _listaPesosEnvases.Max(x => x.Numenvase ?? 0) : 0;
		return maxNumero + 1;
	}

	private async Task EjecutarConCarga(Func<Task> accion)
	{
		Cargando = true;
		await InvokeAsync(() => StateHasChanged());
		await accion();
		Cargando = false;
		await InvokeAsync(() => StateHasChanged());
	}

	private async Task MostrarReporte(int numEnvase)
	{
		NumEnvaseSeleccionado = numEnvase;
		ParameterNombreReport = $"ReporteEtiqueta?NumEnvaseParam={numEnvase}";
		MostrarVentanaReporte = true;
		await InvokeAsync(() => StateHasChanged());
	}
}