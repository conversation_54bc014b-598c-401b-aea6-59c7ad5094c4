﻿namespace GestionAPQ_BLZ.MediaTR.Command;

using Common.ResponseModels.ResponseModels;
using MediatR;
using Repositories.Base.APQLitalsa;

public class DeleteTablaProductosNodrizasCommand : IRequest<SingleResult<int>>
{
    public DeleteTablaProductosNodrizasCommand(int id)
    {
        Id = id;
    }

    public int Id { get; set; }
}

public class DeleteNodrizaCommandHandler : IRequestHandler<DeleteTablaProductosNodrizasCommand, SingleResult<int>>
{
    private readonly ITablaProductosNodrizasRepo _tablaProductosNodrizasRepo;

    public DeleteNodrizaCommandHandler(ITablaProductosNodrizasRepo tablaProductosNodrizasRepo)
    {
        _tablaProductosNodrizasRepo = tablaProductosNodrizasRepo;
    }


    public async Task<SingleResult<int>> Handle(DeleteTablaProductosNodrizasCommand request,
        CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Errors = [],
            Data = 0
        };

        var tablaProductoNodriza =
            await _tablaProductosNodrizasRepo.GetTablaProductosNodrizasById(request.Id, false, cancellationToken);
        if (tablaProductoNodriza == null)
        {
            result.Errors.Add("No se ha encontrado la nodriza en la base de datos.");
            return result;
        }

        var resultDB = await _tablaProductosNodrizasRepo.DeleteEntity(cancellationToken, tablaProductoNodriza);
        result.Data = resultDB;

        return result;
    }
}