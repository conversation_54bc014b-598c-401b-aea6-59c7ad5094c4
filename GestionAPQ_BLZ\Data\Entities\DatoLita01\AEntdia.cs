﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;

namespace GestionAQP_BLZ.Server.Data.Entities.DatoLita01;

public partial class AEntdia
{
    public string Proveedor { get; set; }

    public string Albaran { get; set; }

    public string Codigo { get; set; }

    public string Linea { get; set; }

    public string Descrip { get; set; }

    public string Estadis { get; set; }

    public double? Cantidad { get; set; }

    public string Tipiva { get; set; }

    public double? Precio { get; set; }

    public double? Precio1 { get; set; }

    public double? Descuento { get; set; }

    public double? Iva { get; set; }

    public double? Recargo { get; set; }

    public DateTime? Fecent { get; set; }

    public string Codped { get; set; }

    public string Codfac { get; set; }

    public double? Canfac { get; set; }

    public double? Importe { get; set; }

    public double? Importe1 { get; set; }

    public string Cancela { get; set; }

    public string Undcompra { get; set; }

    public string Undstock { get; set; }

    public double? Coefic { get; set; }

    public string Cancelado { get; set; }

    public string Registro { get; set; }

    public double? <PERSON><PERSON> { get; set; }

    public double? Cambio1 { get; set; }

    public string Moneda { get; set; }

    public string Moneda1 { get; set; }

    public double? Redondeo { get; set; }

    public double? Redondeo1 { get; set; }

    public string Lote { get; set; }

    public string Ubicacion { get; set; }

    public DateTime? Fechalote { get; set; }

    public string Orden { get; set; }

    public string Centroc { get; set; }

    public string Operacion { get; set; }

    public string Numvale { get; set; }

    public string Calidad1 { get; set; }

    public string Calidad2 { get; set; }

    public string Lineaped { get; set; }

    public DateTime? Fentrega { get; set; }

    public double? Bultos { get; set; }

    public string Escliente { get; set; }

    public string Referencia { get; set; }

    public double? Kgsalbaran { get; set; }

    public string Undpedido { get; set; }

    public double? Unidades { get; set; }

    public double? Cantpedido { get; set; }

    public double? Largo { get; set; }

    public double? Ancho { get; set; }

    public double? Gramaje { get; set; }

    public double? Espesor { get; set; }

    public double? Humedad { get; set; }

    public string Litaprodpr { get; set; }

    public string Operario { get; set; }

    public string Sscc { get; set; }
}