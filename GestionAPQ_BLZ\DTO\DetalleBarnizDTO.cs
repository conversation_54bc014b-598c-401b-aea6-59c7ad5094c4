﻿namespace GestionAPQ_BLZ.DTO;

using Genericos;

public class DetalleBarnizDTO
{
    public AArticuDTO? Articulo { get; set; }
    public List<LotesNodrizasDTO>? ListaLotesNodrizas { get; set; }
    public List<ASalidaDTO>? ListaSalidas { get; set; }
    public List<DetalleLoteBarnizDTO>? ListaDetallesLotes { get; set; }
    public List<AViscosDTO>? ListaViscosidades { get; set; }

    /// <summary>
    ///     Replica la fórmula VBA: ="De " & devuelve_datos_producto([Idproducto];[TemperaturaViscosidad];"MIN") & " a " &
    ///     devuelve_datos_producto([Idproducto];[TemperaturaViscosidad];"MAX")
    /// </summary>
    /// <param name="temperatura">Temperatura para la cual calcular el rango de viscosidad</param>
    public string GetRangoViscosidadTeorica(double temperatura)
    {
        if (ListaViscosidades == null || !ListaViscosidades.Any())
            return "Sin datos de viscosidad.";

        var max = GetViscosidadMax(temperatura);
        var min = GetViscosidadMin(temperatura);

        if (min == -9998 || max == -9998)
            return "No hay datos para esa temperatura.";

        return $"De {min:F1} a {max:F1}.";
    }

    /// <summary>
    ///     Migrado de la función VBA 'interpola'.
    /// </summary>
    private double Interpola(double x1, double x2, double y, double y1, double y2)
    {
        return (y - y2) * (x1 - x2) / (y1 - y2) + x2;
    }

    /// <summary>
    ///     Migrado de la función VBA 'devuelve_datos_producto'.
    /// </summary>
    private double GetViscosidadInterpolada(double temperatura, string tipoDato)
    {
        var localizado = false;
        double v = 0;

        // ordenar viscosidades por temperatura
        var viscosidadesOrdenadas = ListaViscosidades
            .Where(v => v.Temp.HasValue)
            .OrderBy(v => v.Temp.Value)
            .ToList();

        if (viscosidadesOrdenadas == null || viscosidadesOrdenadas.Count == 0)
            return v;

        // Obtener el primer registro
        var primerRegistro = viscosidadesOrdenadas.First();
        var t0 = primerRegistro.Temp!.Value;
        var v0 = tipoDato == "MAX" ? primerRegistro.Vmax ?? 0 : primerRegistro.Vmin ?? 0;

        // Si solo hay un registro, verificar si la temperatura coincide exactamente
        if (viscosidadesOrdenadas.Count == 1) return Math.Abs(temperatura - t0) < 0.001 ? v0 : -9998; // Fuera de rango

        // Iterar por los registros para encontrar el rango que contiene la temperatura
        for (var i = 1; i < viscosidadesOrdenadas.Count && !localizado; i++)
        {
            var registroActual = viscosidadesOrdenadas[i];
            var t1 = registroActual.Temp!.Value;
            var v1 = tipoDato == "MAX" ? registroActual.Vmax ?? 0 : registroActual.Vmin ?? 0;

            // Verificar si la temperatura está en el rango [t0, t1]
            if (temperatura <= t1 && temperatura >= t0)
            {
                localizado = true;
                // Interpolar entre los dos puntos
                v = Interpola(v0, v1, temperatura, t0, t1);
            }

            // Preparar para la siguiente iteración
            t0 = t1;
            v0 = v1;
        }

        // Si no se localizó, la temperatura está fuera del rango
        if (!localizado) return -9998; // Temperatura fuera del rango disponible

        return v;
    }

    public double? GetViscosidadMax(double? temperatura)
    {
        return temperatura.HasValue ? GetViscosidadInterpolada(temperatura.Value, "MAX") : null;
    }

    public double? GetViscosidadMin(double? temperatura)
    {
        return temperatura.HasValue ? GetViscosidadInterpolada(temperatura.Value, "MIN") : null;
    }

    public string GetSolidosTeoricos()
    {
        if (!string.IsNullOrEmpty(Articulo?.Unidades.ToString()) &&
            !string.IsNullOrEmpty(Articulo?.Fscpor.ToString()))
            return
                $"{Articulo?.Unidades.ToString()}% \u00b1 {Articulo?.Fscpor.ToString()}";
        return string.Empty;
    }

    public double? GetSolidosMax()
    {
        return Articulo?.Unidades + Articulo?.Fscpor;
    }

    public double? GetSolidosMin()
    {
        return Articulo?.Unidades - Articulo?.Fscpor;
    }

    public string GetEstadoSolidos()
    {
        return Articulo?.Fscpor <= GetSolidosMax() + 1 && Articulo?.Fscpor >= GetSolidosMin() ? string.Empty : "NO OK";
    }

    public string GetEstadoViscosidad(double? temperatura, int? viscosidad)
    {
        if (temperatura.HasValue)
        {
            var viscoMax = GetViscosidadMax(temperatura);
            var viscoMin = GetViscosidadMin(temperatura);

            if (viscoMin < 0 || viscoMin < 0) return "N/A";

            if (viscosidad <= viscoMax && viscosidad >= viscoMin)
                return "OK";
            return "NO OK";
        }

        return "SIN TEMP.";
    }
}