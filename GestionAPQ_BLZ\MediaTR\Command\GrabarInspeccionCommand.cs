namespace GestionAPQ_BLZ.MediaTR.Command;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.Base.APQLitalsa;

public record GrabarInspeccionCommand(TablaInspeccionesDTO Inspeccion) : IRequest<SingleResult<int>>;

public class GrabarInspeccionCommandHandler : IRequestHandler<GrabarInspeccionCommand, SingleResult<int>>
{
    private readonly ITablaInspeccionesRepo _tablaInspeccionesRepo;

    public GrabarInspeccionCommandHandler(ITablaInspeccionesRepo tablaInspeccionesRepo)
    {
        _tablaInspeccionesRepo = tablaInspeccionesRepo;
    }

    public async Task<SingleResult<int>> Handle(GrabarInspeccionCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Data = 0,
            Errors = []
        };

        if (!request.Inspeccion.Idproducto.HasValue)
        {
            result.Errors.Add("Debes indicar un producto.");
            return result;
        }

        if (string.IsNullOrEmpty(request.Inspeccion.Idlote))
        {
            result.Errors.Add("Debes indicar el lote");
            return result;
        }

        if (string.IsNullOrEmpty(request.Inspeccion.RealizadoPor))
        {
            result.Errors.Add("Debes seleccionar un operario.");
            return result;
        }

        // validaciones adicionales cuando la inspección está marcada como realizada
        if (request.Inspeccion.Inspeccion)
        {
            if (!request.Inspeccion.Viscosidad.HasValue)
            {
                result.Errors.Add("Debe especificar la viscosidad cuando la inspección está marcada como realizada.");
                return result;
            }

            if (!request.Inspeccion.Solidos.HasValue)
            {
                result.Errors.Add("Debe especificar los sólidos cuando la inspección está marcada como realizada.");
                return result;
            }

            if (!request.Inspeccion.TemperaturaViscosidad.HasValue)
            {
                result.Errors.Add(
                    "Debe especificar la temperatura de viscosidad cuando la inspección está marcada como realizada.");
                return result;
            }
        }

        TablaInspecciones? inspeccion;

        // si tiene ID, es una actualización
        if (request.Inspeccion.IdInspecciones != null)
        {
            inspeccion = await _tablaInspeccionesRepo.GetInspeccionPorId(request.Inspeccion.IdInspecciones.Value, false,
                cancellationToken);

            if (inspeccion == null)
            {
                result.Errors.Add("No se ha encontrado la inspección en la base de datos.");
                return result;
            }

            // actualizamos campos
            inspeccion.Viscosidad = request.Inspeccion.Viscosidad;
            inspeccion.Solidos = request.Inspeccion.Solidos;
            inspeccion.ObservacionesAplicacion = request.Inspeccion.ObservacionesAplicacion;
            inspeccion.Inspeccion = request.Inspeccion.Inspeccion;
            inspeccion.RealizadoPor = request.Inspeccion.RealizadoPor;
            inspeccion.TemperaturaViscosidad = request.Inspeccion.TemperaturaViscosidad;
            inspeccion.ObservacionesQ = request.Inspeccion.ObservacionesQ;
            inspeccion.Fecha = DateTime.Now;
        }
        else
        {
            // es una nueva inspección
            inspeccion = TinyMapper.Map<TablaInspecciones>(request.Inspeccion);
            inspeccion.Fecha = DateTime.Now;
        }

        var resultDb = await _tablaInspeccionesRepo.UpdateEntityWithId(cancellationToken, inspeccion);
        result.Data = resultDb;

        return result;
    }
}