﻿namespace GestionAPQ_BLZ.Services.Base;

using DevExpress.Blazor;

public interface ICustomDialogService : IDialogService
{
    Task<bool> MostrarConfirmacionEstiloSuccess(string titulo, string mensaje, string txtBtnOk,
        string txtBtnCancel, string? width = null);

    Task<bool> MostrarConfirmacionEstiloDanger(string titulo, string mensaje, string txtBtnOk,
        string txtBtnCancel, string? width = null);

    Task<bool> MostrarConfirmacionEstiloWarning(string titulo, string mensaje, string txtBtnOk,
        string txtBtnCancel, string? width = null);

    Task MostrarInfo(string titulo, string mensaje, string txtBtnOk, string? width = null);

    Task MostrarError(string titulo, string mensaje, string txtBtnOk, string? width = null);

    Task MostrarWarning(string titulo, string mensaje, string txtBtnOk, string? width = null);
}