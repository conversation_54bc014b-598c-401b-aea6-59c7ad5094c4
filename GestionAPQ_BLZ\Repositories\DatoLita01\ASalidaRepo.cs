﻿namespace GestionAPQ_BLZ.Repositories.DatoLita01;

using Base;
using Base.DatoLita01;
using GestionAQP_BLZ.Server.Data.DbContexts.DatoLita01;
using GestionAQP_BLZ.Server.Data.Entities.DatoLita01;

public class ASalidaRepo : Repository<ASalida, DatoLita01Context>, IASalidaRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public ASalidaRepo(DatoLita01Context dbContext) : base(dbContext)
    {
    }

    public async Task<List<ASalida>> GetSalidasByIdProducto(string idProducto, bool asNoTracking,
        CancellationToken cancellationToken)
    {
        return await GetList(asNoTracking, cancellationToken,
            i =>
                i.Estadis.StartsWith("BABA")
                && !i.Numero.StartsWith("T")
                && i.Codigo == idProducto);
    }
}