﻿namespace GestionAPQ_BLZ;

using Blazr.RenderState.Server;
using Common.ResponseModels.ResponseModels;
using DevExpress.AspNetCore.Reporting;
using DevExpress.Blazor;
using DevExpress.Blazor.Reporting;
using DevExpress.XtraReports.Services;
using DevExpress.XtraReports.Web.Extensions;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.DatoLita01;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.DatoLita01;
using MediatR;
using MediaTR.Query;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using Repositories.APQLitalsa;
using Repositories.Base;
using Repositories.Base.APQLitalsa;
using Repositories.Base.DatoLita01;
using Repositories.DatoLita01;
using Services;
using Services.Base;

public static class DependencyInjection
{
    public static WebApplicationBuilder CompleteWebUiConfig(this WebApplicationBuilder builder)
    {
        // CONVENCIÓN BLAZOR
        builder.Services.AddRazorComponents()
            .AddInteractiveServerComponents()
            .AddInteractiveWebAssemblyComponents();

        // BLAZRRENDERSTATE
        builder.AddBlazrRenderStateServerServices();


        // DEVEXPRESS
        builder.Services.AddDevExpressBlazor(options =>
        {
            options.BootstrapVersion = BootstrapVersion.v5;
            options.SizeMode = SizeMode.Medium;
        });
        builder.Services.AddDevExpressBlazorReporting();
        builder.Services.ConfigureReportingServices(configurator =>
        {
            configurator.ConfigureReportDesigner(designerConfigurator => { });
            configurator.UseAsyncEngine();
        });
        builder.Services.AddRazorPages(); // necesario tmbn para que funcione dxdocumentviewer
        builder.Services.AddScoped<IReportProviderAsync, Services.CustomReportProviderAsync>();


        // SCOPES
        builder.Services.AddScoped<ILocalStorageService, LocalStorageService>();
        builder.Services.AddScoped<ICustomToastService, CustomToastService>();
        builder.Services.AddScoped<ICustomDialogService, CustomDialogService>();
        builder.Services.AddScoped<IDocumentService, DocumentService>();

        return builder;
    }

    public static WebApplicationBuilder CompleteApplicationConfig(this WebApplicationBuilder builder)
    {
        // TINYMAPPER
        SetMapeos();

        // MEDIATR
        builder.Services.AddMediatR(
            cfg => { cfg.RegisterServicesFromAssemblies(typeof(DependencyInjection).Assembly); });

        // HANDLERS GENÉRICOS ESPECÍFICOS
        builder.Services
            .AddScoped<IRequestHandler<GetAllEntitiesQuery<TablaOperarios, TablaOperariosDTO>,
                    ListResult<TablaOperariosDTO>>,
                GetAllEntitiesQueryHandler<TablaOperarios, TablaOperariosDTO>>();
        builder.Services
            .AddScoped<IRequestHandler<GetAllEntitiesQuery<TablaProductosNodrizas, TablaProductosNodrizasDTO>,
                    ListResult<TablaProductosNodrizasDTO>>,
                GetAllEntitiesQueryHandler<TablaProductosNodrizas, TablaProductosNodrizasDTO>>();
        builder.Services
            .AddScoped<IRequestHandler<GetAllEntitiesQuery<TablaPesosEnvases, TablaPesosEnvasesDTO>,
                    ListResult<TablaPesosEnvasesDTO>>,
                GetAllEntitiesQueryHandler<TablaPesosEnvases, TablaPesosEnvasesDTO>>();
        builder.Services
            .AddScoped<IRequestHandler<GetAllEntitiesQuery<TablaInspecciones, TablaInspeccionesDTO>,
                    ListResult<TablaInspeccionesDTO>>,
                GetAllEntitiesQueryHandler<TablaInspecciones, TablaInspeccionesDTO>>();

        //builder.Services
        //    .AddScoped<IRequestHandler<GetAllEntitiesQuery_<TablaOperarios, TablaOperariosDTO>,
        //            ListResult<TablaOperariosDTO>>,
        //        GetAllEntitiesQueryHandler_<TablaOperarios, TablaOperariosDTO>>();

        return builder;
    }

    public static WebApplicationBuilder CompleteInfraestructureConfig(this WebApplicationBuilder builder)
    {
        // IN2CONTEXT
        builder.Services.AddDbContext<DatoLita01Context>(options =>
        {
            options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnectionDatoLita01"));
        });
        builder.Services.AddScoped<IAArticuRepo, AArticuRepo>();
        builder.Services.AddScoped<IASalidaRepo, ASalidaRepo>();
        builder.Services.AddScoped<IALotessRepo, ALotessRepo>();
        builder.Services.AddScoped<IAEntdiaRepo, AEntdiaRepo>();
        builder.Services.AddScoped<IAViscosRepo, AViscosRepo>();

        // APQLITALSACONTEXT
        builder.Services.AddDbContext<APQLitalsaContext>(options =>
        {
            options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnectionApqLitalsa"));
        });
        builder.Services.AddScoped<ITablaOperariosRepo, TablaOperariosRepo>();
        builder.Services.AddScoped<ITablaProductosNodrizasRepo, TablaProductosNodrizasRepo>();
        builder.Services.AddScoped<ILotesNodrizasRepo, LotesNodrizasRepo>();
        builder.Services.AddScoped<IIncidenciasRepo, IncidenciasRepo>();
        builder.Services.AddScoped<ITablaInspeccionesRepo, TablaInspeccionesRepo>();
        builder.Services.AddScoped<ITablaPesosEnvasesRepo, TablaPesosEnvasesRepo>();

        // PARA USAR QUERY GENÉRICA
        builder.Services.AddScoped<IRepository<TablaOperarios>>(provider =>
            provider.GetRequiredService<ITablaOperariosRepo>());
        builder.Services.AddScoped<IRepository<TablaProductosNodrizas>>(provider =>
            provider.GetRequiredService<ITablaProductosNodrizasRepo>());
        builder.Services.AddScoped<IRepository<TablaInspecciones>>(provider =>
            provider.GetRequiredService<ITablaInspeccionesRepo>());
        builder.Services.AddScoped<IRepository<TablaPesosEnvases>>(provider =>
            provider.GetRequiredService<ITablaPesosEnvasesRepo>());

        return builder;
    }

    private static void SetMapeos()
    {
        SetMapeoEstandar<AArticu, AArticuDTO>();
        SetMapeoEstandar<AEntdia, AEntdiaDTO>();
        SetMapeoEstandar<ALotess, ALotessDTO>();
        SetMapeoEstandar<ASalida, ASalidaDTO>();
        SetMapeoEstandar<AViscos, AViscosDTO>();
        SetMapeoEstandar<Incidencias, IncidenciaDTO>();
        SetMapeoEstandar<Lotesnodrizas, LotesNodrizasDTO>();
        SetMapeoEstandar<TablaInspecciones, TablaInspeccionesDTO>();
        SetMapeoEstandar<TablaOperarios, TablaOperariosDTO>();
        SetMapeoEstandar<TablaProductosNodrizas, TablaProductosNodrizasDTO>();
        SetMapeoEstandar<TablaPesosEnvases, TablaPesosEnvasesDTO>();
    }

    private static void SetMapeoEstandar<TEntity, TDto>()
    {
        TinyMapper.Bind<TEntity, TDto>();
        TinyMapper.Bind<TDto, TEntity>();
        TinyMapper.Bind<List<TEntity>, List<TDto>>();
        TinyMapper.Bind<List<TDto>, List<TEntity>>();
    }
}