﻿namespace GestionAPQ_BLZ.Repositories.Base.DatoLita01;

using System.Linq.Expressions;
using GestionAQP_BLZ.Server.Data.Entities.DatoLita01;

public interface IAArticuRepo : IRepository<AArticu>
{
    public IQueryable<AArticu> GetIQueryableBarnices(string? idBarniz, bool asNoTracking);

    public Task<List<AArticu>> GetBarnices(string? idBarniz, bool asNoTracking,
        CancellationToken cancellationToken);

    public Task<List<(AArticu, ALotess)>> GetBarnicesInnerJoinLotesConStock(string? idBarniz,
        bool asNoTracking,
        CancellationToken cancellationToken);

    public Task<List<(AArticu, ALotess, AEntdia)>> GetBarnicesInnerJoinLotesConStockLeftJoinPrimerAEntdia(
        string? idBarniz,
        bool asNoTracking,
        Expression<Func<(<PERSON>rticu, ALotess, AEntdia), object>>? groupBy,
        CancellationToken cancellationToken);

    public Task<List<(AArticu, AViscos)>> GetBarnicesLeftJoinViscosidades(string? idBarniz,
        bool asNoTracking, CancellationToken cancellationToken);
}