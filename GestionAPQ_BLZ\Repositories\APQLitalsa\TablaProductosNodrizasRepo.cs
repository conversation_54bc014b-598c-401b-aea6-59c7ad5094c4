﻿namespace GestionAPQ_BLZ.Repositories.APQLitalsa;

using Base;
using Base.APQLitalsa;
using GestionAQP_BLZ.Server.Data.DbContexts.APQLitalsa;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public class TablaProductosNodrizasRepo : Repository<TablaProductosNodrizas, APQLitalsaContext>,
    ITablaProductosNodrizasRepo
{
    // : IRepository<T> // obliga a implementar en clase todos los métodos de IRepository
    // aquí irían solo los métodos de dominio
    public TablaProductosNodrizasRepo(APQLitalsaContext dbContext) : base(dbContext)
    {
    }

    public Task<TablaProductosNodrizas?> GetTablaProductosNodrizasById(int id, bool asNoTracking,
        CancellationToken cancellationToken)
    {
        return GetFirstOrDefault(asNoTracking, cancellationToken,
            i => i.Id == id);
    }

    public Task<TablaProductosNodrizas?> GetTablaProductosNodrizasByNumNodriza(int idNodriza,
        bool asNoTracking, CancellationToken cancellationToken)
    {
        return GetFirstOrDefault(asNoTracking, cancellationToken,
            i => i.IdNodriza.HasValue && i.IdNodriza == idNodriza);
    }
}