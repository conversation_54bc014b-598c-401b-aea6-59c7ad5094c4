@inject IMediator Mediator
@inject ICustomToastService ToastService
@inject ILocalStorageService LocalStorageService
@inject IJSRuntime Js
@inject IDocumentService DocumentService

<DxPopup @bind-Visible="Visible"
         HeaderText="Revisar Inspección"
         ShowCloseButton="true"
         CloseOnOutsideClick="false"
         ShowFooter="true"
         Height="auto"
         MaxHeight="90vh"
         MinHeight="400px"
         Width="825px"
         MaxWidth="90vw"
         Closing="HandleClosingPopup"
         Closed="async () => await HandleClosedPopup()">
	<BodyTemplate Context="popupContext">
		@* metemos aquí el loadingpangel porque sino no pilla bien el positiontarget *@
		<CustomLoadingPanel @bind-Visible="Cargando"
		                    PositionTarget=".popup-content"
		                    IsContentVisible="false"
		                    ApplyBackgroundShading="false"/>
		<div class="popup-content p-3" style="overflow-x: hidden; overflow-y: auto;">
			<DxFormLayout>
				<DxFormLayoutGroup Caption="Datos Inspección" ColSpanMd="12">
					<DxFormLayoutItem Caption="Producto:" ColSpanMd="8" CaptionPosition="CaptionPosition.Vertical">
						<DxComboBox Data="_detallesBarnices"
						            @bind-Value="DetallesBarnizSelected"
						            NullText="Selecciona un producto..."
						            SearchFilterCondition="ListSearchFilterCondition.Contains"
						            SearchMode="ListSearchMode.AutoSearch"
						            ReadOnly="true"
						            CssClass="w-100"
						            EditFormat="{0}, {1}">
							<DxListEditorColumn FieldName="@($"{nameof(DetalleBarnizDTO.Articulo)}.{nameof(DetalleBarnizDTO.Articulo.Codigo)}")" Caption="Producto"/>
							<DxListEditorColumn FieldName="@($"{nameof(DetalleBarnizDTO.Articulo)}.{nameof(DetalleBarnizDTO.Articulo.Descrip)}")" Caption="Descripción"/>
						</DxComboBox>
					</DxFormLayoutItem>

					<DxFormLayoutItem Caption="Lote:" ColSpanMd="4" CaptionPosition="CaptionPosition.Vertical">
						<DxTextBox @bind-Text="InspeccionActual.Idlote"
						           ReadOnly="true"
						           CssClass="w-100"/>
					</DxFormLayoutItem>
				</DxFormLayoutGroup>

				<DxFormLayoutGroup Caption="Datos Técnicos" ColSpanMd="12">
					<DxFormLayoutItem Caption="Viscosidad:" ColSpanMd="2" CaptionPosition="CaptionPosition.Vertical">
						<DxSpinEdit @bind-Value="InspeccionActual.Viscosidad"
						            ReadOnly="!CamposEditablesHabilitados"
						            CssClass="w-100"/>
					</DxFormLayoutItem>

					<DxFormLayoutItem Caption="Seg.Copa Ford Nº4:" ColSpanMd="3" CaptionPosition="CaptionPosition.Vertical">
						<DxSpinEdit @bind-Value="InspeccionActual.TemperaturaViscosidad"
						            BindValueMode="BindValueMode.OnInput"
						            ReadOnly="!CamposEditablesHabilitados"
						            CssClass="w-100"/>
					</DxFormLayoutItem>

					<DxFormLayoutItem Caption="ºC Teórico:" ColSpanMd="5" CaptionPosition="CaptionPosition.Vertical">
						<DxTextBox Text="@RangoViscosidadTeorica" ReadOnly="true" CssClass="w-100"/>
					</DxFormLayoutItem>

					<DxFormLayoutItem Caption="" ColSpanMd="2" CaptionPosition="CaptionPosition.Vertical">
						<DxButton RenderStyle="ButtonRenderStyle.Info"
						          Click="@(async () => await HandleClickConsultarIT("IT1"))"
								  Text="Ver IT"
						          IconCssClass="bi bi-file-earmark-text"
						          CssClass="w-100 mt-3"/>
					</DxFormLayoutItem>
					<DxFormLayoutItem Caption="Sólidos:" ColSpanMd="3" CaptionPosition="CaptionPosition.Vertical">
						<DxSpinEdit @bind-Value="InspeccionActual.Solidos"
						            DisplayFormat="F2"
						            ReadOnly="!CamposEditablesHabilitados"
						            CssClass="w-100"/>
					</DxFormLayoutItem>

					<DxFormLayoutItem Caption="Teórico:" ColSpanMd="3" CaptionPosition="CaptionPosition.Vertical">
						<DxTextBox Text="@SolidosTeoricos" ReadOnly="true" CssClass="w-100" />
					</DxFormLayoutItem>

					<DxFormLayoutItem Caption="" ColSpanMd="2" CaptionPosition="CaptionPosition.Vertical">
						<DxButton RenderStyle="ButtonRenderStyle.Info"
								  Click="@(async () => await HandleClickConsultarIT("IT2"))"
								  Text="Ver IT"
								  IconCssClass="bi bi-file-earmark-text"
								  CssClass="w-100 mt-3"/>
					</DxFormLayoutItem>

					<DxFormLayoutItem Caption="Notas:" ColSpanMd="4" CaptionPosition="CaptionPosition.Vertical">
						<DxTextBox Text="@Notas" ReadOnly="true" CssClass="w-100"/>
					</DxFormLayoutItem>
				</DxFormLayoutGroup>

				<DxFormLayoutGroup Caption="Observaciones y Control" ColSpanMd="12">
					<DxFormLayoutItem Caption="Observaciones Aplicación:" ColSpanMd="12" CaptionPosition="CaptionPosition.Vertical">
						<DxTextBox @bind-Text="InspeccionActual.ObservacionesAplicacion"
						           ReadOnly="!CamposEditablesHabilitados"
						           CssClass="w-100"/>
					</DxFormLayoutItem>
					<DxFormLayoutItem Caption="Fecha:" ColSpanMd="4" CaptionPosition="CaptionPosition.Vertical">
						<DxDateEdit @bind-Date="InspeccionActual.Fecha"
						            DisplayFormat="{0:dd/MM/yyyy HH:mm:ss}"
						            ReadOnly="true"
						            CssClass="w-100"/>
					</DxFormLayoutItem>

					<DxFormLayoutItem Caption="Realizada por:" ColSpanMd="4" CaptionPosition="CaptionPosition.Vertical">
						<DxTextBox @bind-Text="InspeccionActual.RealizadoPor"
						           ReadOnly="true"
						           CssClass="w-100"/>
					</DxFormLayoutItem>

					<DxFormLayoutItem Caption="Operario actual:" ColSpanMd="4" CaptionPosition="CaptionPosition.Vertical">
						<DxComboBox Data="_ddOperarios"
						            @bind-Value="OperarioActualSelected"
						            NullText="Selecciona un operario..."
						            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto"
						            ValueFieldName="@nameof(TablaOperariosDTO.Id)"
						            TextFieldName="@nameof(TablaOperariosDTO.Operario)"
						            SearchFilterCondition="ListSearchFilterCondition.Contains"
						            SearchMode="ListSearchMode.AutoSearch"
						            CssClass="w-100"/>
					</DxFormLayoutItem>
					<DxFormLayoutItem ColSpanMd="12" CaptionPosition="CaptionPosition.Vertical">
						<div class="d-flex justify-content-end mt-2 align-items-center">
							<label class="me-2">Inspección Realizada:</label>
							<DxCheckBox @bind-Checked="InspeccionActual.Inspeccion"/>
						</div>
					</DxFormLayoutItem>
				</DxFormLayoutGroup>
			</DxFormLayout>
		</div>
	</BodyTemplate>

	<FooterContentTemplate>
		<div class="d-flex justify-content-between w-100 py-2 px-2">
			<DxButton RenderStyle="ButtonRenderStyle.Primary"
			          Click="async() => await HandleClickAbrirFichaTecnica()"
			          Enabled="!Cargando"
			          Text="Ficha Técnica"
			          IconCssClass="bi bi-file-earmark-pdf"
			          CssClass="popup-button"/>
			<div>
				<DxButton SubmitFormOnClick="true"
				          Text="Grabar"
				          RenderStyle="ButtonRenderStyle.Success"
				          RenderStyleMode="ButtonRenderStyleMode.Contained"
				          CssClass="px-3"
						  IconCssClass="bi bi-check-circle me-1"
						  Click="HandleClickGrabar" />
				<DxButton RenderStyle="ButtonRenderStyle.Secondary"
				          RenderStyleMode="ButtonRenderStyleMode.Contained"
				          IconCssClass="bi bi-x-circle me-1"
				          CssClass="px-3"
						  Text="Cancelar"
						  Click="HandleClickCancelar" />
			</div>
		</div>
	</FooterContentTemplate>
</DxPopup>

@code {
	[Parameter] public bool Visible { get; set; }
	[Parameter] public EventCallback<bool> VisibleChanged { get; set; }

	private List<DetalleBarnizDTO> _detallesBarnices = [];
	private List<TablaOperariosDTO> _ddOperarios = [];

	private bool Cargando { get; set; }
	private string RangoViscosidadTeorica
	{
		get
		{
			if (DetallesBarnizSelected != null && InspeccionActual?.TemperaturaViscosidad.HasValue == true)
			{
				return DetallesBarnizSelected.GetRangoViscosidadTeorica(
					(double)InspeccionActual.TemperaturaViscosidad.Value);
			}
			return string.Empty;
		}
	}
	private string Notas
	{
		get
		{
			if (DetallesBarnizSelected?.Articulo?.Tempseca1.HasValue == true)
				return $"Regular balanza sólidos a {DetallesBarnizSelected.Articulo.Tempseca1.Value} ºC.";
			return string.Empty;
		}
	}
	private string SolidosTeoricos => DetallesBarnizSelected?.GetSolidosTeoricos() ?? string.Empty;

	private TablaInspeccionesDTO InspeccionActual { get; set; } = new();
	private DetalleBarnizDTO? DetallesBarnizSelected { get; set; }
	private TablaOperariosDTO? OperarioActualSelected { get; set; }
	private bool CamposEditablesHabilitados => !InspeccionActual.Inspeccion;


	public void ResetValoresPorDefecto()
	{
		InspeccionActual = new TablaInspeccionesDTO
		{
			Fecha = DateTime.Now,
			Inspeccion = false
		};
		DetallesBarnizSelected = null;
	}

	public async Task CargarDatos(int idProducto, string lote, bool inspeccionRealizada)
	{
		await EjecutarConCarga(async () =>
		{
			// ejecutamos querys
			var resultOperarios = await Mediator.Send(new GetAllEntitiesQuery<TablaOperarios, TablaOperariosDTO>());
			var resultProductos = await Mediator.Send(new GetDetallesBarnicesQuery(idProducto.ToString(), true)); // true para cargar viscosidades

			// comprobamos si hay errores y mostramos dado el caso
			var listaErrores = resultOperarios.Errors.Concat(resultProductos.Errors).ToArray();
			if (listaErrores.Any())
			{
				ToastService.MostrarError(listaErrores.First());
				return;
			}

			// asignamos valor a variables
			_ddOperarios = resultOperarios.Data;
			_detallesBarnices = resultProductos.Data.OrderBy(i => i.Articulo?.Codigo).ToList();

			// Cargar operario actual desde localStorage (cookie)
			await CargarOperarioActualDesdeLocalStorage();

			// SIEMPRE buscar si existe una inspección por producto y lote
			var resultInspeccion = await Mediator.Send(new GetInspeccionByProductoLoteQuery(idProducto, lote));
			if (resultInspeccion.Errors.Any())
			{
				ToastService.MostrarError(resultInspeccion.Errors.First());
				return;
			}

			// verificar si se encontró la inspección
			if (resultInspeccion.Data != null)
			{
				// Se encontró inspección existente
				InspeccionActual = resultInspeccion.Data;
				DetallesBarnizSelected = _detallesBarnices.FirstOrDefault(p => p.Articulo?.Codigo == idProducto.ToString()) ??
										 new DetalleBarnizDTO { Articulo = new AArticuDTO { Codigo = idProducto.ToString() } };
			}
			else
			{
				// no se encontró inspección, cuando se supone que debería haber una
				if (inspeccionRealizada)
				{
					HandleClickCerrarPopup();
					ToastService.MostrarError($"No se encontró la inspección para el producto {idProducto} y lote {lote}.");
					return;
				}

				// Crear nueva inspección
				InspeccionActual = new TablaInspeccionesDTO
				{
					Idproducto = idProducto,
					Idlote = lote,
					Fecha = DateTime.Now,
					Inspeccion = false
				};
				DetallesBarnizSelected = _detallesBarnices.FirstOrDefault(p => p.Articulo?.Codigo == idProducto.ToString()) ??
										 new DetalleBarnizDTO { Articulo = new AArticuDTO { Codigo = idProducto.ToString() } };
			}
		});
	}

	private void HandleClosingPopup(PopupClosingEventArgs e)
	{
		// si está ejecutando operaciones, no cerramos popup
		if (Cargando)
			e.Cancel = true;
	}

	private async Task HandleClosedPopup()
	{
		await VisibleChanged.InvokeAsync(Visible); // avisamos a los suscritos
	}

	private async Task HandleClickConsultarIT(string tipoItDoc)
	{
		var filePath = DocumentService.GetRutaDocumento(tipoItDoc);
		if (filePath != null)
		{
			var success = await DocumentService.AbrirDocumento(filePath);
			if (!success)
				ToastService.MostrarError($"No se pudo abrir {tipoItDoc}");
		}
		else
		{
			ToastService.MostrarWarning($"No se encontró {tipoItDoc}");
		}
	}

	private async Task HandleClickAbrirFichaTecnica()
	{
		var filePath = DocumentService.GetRutaDocumento("FichaTecnica", DetallesBarnizSelected?.Articulo?.Codigo);
		if (filePath != null)
		{
			var success = await DocumentService.AbrirDocumento(filePath);
			if (!success)
				ToastService.MostrarError("No se pudo abrir la ficha técnica");
		}
		else
		{
			ToastService.MostrarWarning("No se encontró la ficha técnica");
		}
	}

	private async Task CargarOperarioActualDesdeLocalStorage()
	{
		var operarioActual = await LocalStorageService.GetItemAsync<TablaOperariosDTO>(EnumKeysLocalStorage.Operario);
		if (operarioActual is not null && _ddOperarios.Any())
			operarioActual = _ddOperarios.FirstOrDefault(i => i.Id == operarioActual.Id && i.Operario == operarioActual.Operario);
		OperarioActualSelected = operarioActual;
	}

	private async Task HandleClickGrabar()
	{
		await EjecutarConCarga(async () =>
		{
			// asignamos valores
			var oldOperario = InspeccionActual.RealizadoPor;
			InspeccionActual.Idproducto = int.Parse(DetallesBarnizSelected.Articulo.Codigo);
			InspeccionActual.RealizadoPor = OperarioActualSelected?.Operario;

			// ejecutamos mediatr
			var command = new GrabarInspeccionCommand(InspeccionActual);
			var result = await Mediator.Send(command);

			if (result.Errors.Any())
			{
				InspeccionActual.RealizadoPor = oldOperario;
				ToastService.MostrarError(result.Errors.First());
				return;
			}

			HandleClickCerrarPopup();
			ToastService.MostrarOk("Inspección guardada correctamente");
		});
	}

	private void HandleClickCancelar()
	{
		HandleClickCerrarPopup();
	}

	private void HandleClickCerrarPopup()
	{
		Visible = false;
	}

	private async Task EjecutarConCarga(Func<Task> accion)
	{
		Cargando = true;
		await InvokeAsync(() => StateHasChanged());
		await accion();
		Cargando = false;
		await InvokeAsync(() => StateHasChanged());
	}
}