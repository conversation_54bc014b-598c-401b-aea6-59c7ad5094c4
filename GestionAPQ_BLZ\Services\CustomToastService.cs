﻿namespace GestionAPQ_BLZ.Services;

using Base;
using DevExpress.Blazor;
using DevExpress.Blazor.Internal;

public class CustomToastService : ToastNotificationService, ICustomToastService
{
    public CustomToastService(IToastManageService toastManageService) : base(toastManageService)
    {
    }

    public void MostrarError(string mensaje)
    {
        ShowToast(new ToastOptions
        {
            Title = mensaje,
            ThemeMode = ToastThemeMode.Saturated,
            RenderStyle = ToastRenderStyle.Danger
        });
    }

    public void MostrarOk(string mensaje)
    {
        ShowToast(new ToastOptions
        {
            Title = mensaje,
            ThemeMode = ToastThemeMode.Saturated,
            RenderStyle = ToastRenderStyle.Success
        });
    }

    public void MostrarWarning(string mensaje)
    {
        ShowToast(new ToastOptions
        {
            Title = mensaje,
            ThemeMode = ToastThemeMode.Saturated,
            RenderStyle = ToastRenderStyle.Warning
        });
    }
}