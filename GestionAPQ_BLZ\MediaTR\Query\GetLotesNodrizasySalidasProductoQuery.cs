﻿namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO;
using DTO.Genericos;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.Base.APQLitalsa;
using Repositories.Base.DatoLita01;

public record GetLotesNodrizasySalidasProductoQuery(int IdProducto)
    : IRequest<SingleResult<DetalleBarnizDTO>>;

public class
    GetLotesNodrizasySalidasProductoQueryHandler : IRequestHandler<GetLotesNodrizasySalidasProductoQuery,
    SingleResult<DetalleBarnizDTO>>
{
    private readonly IASalidaRepo _aSalidaRepo;
    private readonly ILotesNodrizasRepo _lotesNodrizasRepo;

    public GetLotesNodrizasySalidasProductoQueryHandler(ILotesNodrizasRepo lotesNodrizasRepo, IASalidaRepo aSalidaRepo)
    {
        _lotesNodrizasRepo = lotesNodrizasRepo;
        _aSalidaRepo = aSalidaRepo;
    }

    public async Task<SingleResult<DetalleBarnizDTO>> Handle(
        GetLotesNodrizasySalidasProductoQuery request,
        CancellationToken cancellationToken)
    {
        var result = new SingleResult<DetalleBarnizDTO>
        {
            Data = null,
            Errors = []
        };

        // query para sacar los lotes del barniz
        var listaLotes =
            await _lotesNodrizasRepo.GetLotesNodrizaByIdProducto(request.IdProducto, true,
                cancellationToken);

        // query para sacar todas las salidas del barniz
        var listaSalidas =
            await _aSalidaRepo.GetSalidasByIdProducto(request.IdProducto.ToString(), true,
                cancellationToken);

        // cargamos data en result
        var listaLotesDto = TinyMapper.Map<List<LotesNodrizasDTO>>(listaLotes);
        var listaSalidasDto = TinyMapper.Map<List<ASalidaDTO>>(listaSalidas);
        result.Data = new DetalleBarnizDTO
        {
            ListaLotesNodrizas = listaLotesDto,
            ListaSalidas = listaSalidasDto
        };

        return result;
    }
}