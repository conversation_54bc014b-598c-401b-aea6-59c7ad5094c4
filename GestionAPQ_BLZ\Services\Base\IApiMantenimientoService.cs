﻿namespace GestionAPQ_BLZ.Services.Base;

using Common.ResponseModels.ResponseModels;

public interface IApiMantenimientoService
{
    public Task<SingleResult<T>> GetSingleResult<T>(string url);
    public Task<ListResult<T>> GetListResult<T>(string url);
    public Task<HttpResponseMessage> PostFromForm(string url, List<KeyValuePair<string, string>> listaValores);
    public Task<HttpResponseMessage> PutFromForm(string url, List<KeyValuePair<string, string>> listaValores);
}