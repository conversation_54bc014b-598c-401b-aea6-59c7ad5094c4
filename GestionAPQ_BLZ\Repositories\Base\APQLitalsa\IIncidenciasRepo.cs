﻿namespace GestionAPQ_BLZ.Repositories.Base.APQLitalsa;

using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;

public interface IIncidenciasRepo : IRepository<Incidencias>
{
    public Task<Incidencias?> GetIncidenciaPorId(int id, bool asNoTracking, CancellationToken cancellationToken);

    public Task<Incidencias?> GetIncidenciaByIdProductoxLotexUbicacion(string codigo, string lote,
        string ubicacion, bool asNoTracking, CancellationToken cancellationToken);
}