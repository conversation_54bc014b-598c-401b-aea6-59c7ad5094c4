﻿namespace GestionAPQ_BLZ.MediaTR.Query;

using Common.ResponseModels.ResponseModels;
using DTO;
using DTO.Genericos;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.Base.DatoLita01;

public record GetDetallesBarnicesQuery(string? IdBarniz, bool SacarViscosidades)
    : IRequest<ListResult<DetalleBarnizDTO>>;

public class
    GetDetallesBarnicesQueryQueryHandler : IRequestHandler<GetDetallesBarnicesQuery, ListResult<DetalleBarnizDTO>>
{
    private readonly IAArticuRepo _aArticuRepo;

    public GetDetallesBarnicesQueryQueryHandler(IAArticuRepo aArticuRepo)
    {
        _aArticuRepo = aArticuRepo;
    }

    public async Task<ListResult<DetalleBarnizDTO>> Handle(GetDetallesBarnicesQuery request,
        CancellationToken cancellationToken)
    {
        var result = new ListResult<DetalleBarnizDTO>
        {
            Data = [],
            Errors = []
        };

        if (request.SacarViscosidades)
        {
            var listaBarnicesConViscosidades = await _aArticuRepo.GetBarnicesLeftJoinViscosidades(
                request.IdBarniz, true, cancellationToken);

            var gruposPorArticulo = listaBarnicesConViscosidades
                .GroupBy(x => x.Item1.Codigo)
                .Select(g => new DetalleBarnizDTO
                {
                    Articulo = TinyMapper.Map<AArticuDTO>(g.First().Item1),
                    ListaViscosidades = g.Where(x => x.Item2 != null)
                        .Select(x => TinyMapper.Map<AViscosDTO>(x.Item2))
                        .ToList()
                }).ToList();

            result.Data = gruposPorArticulo;
        }
        else
        {
            var listaBarnices = await _aArticuRepo.GetBarnices(request.IdBarniz, true, cancellationToken);
            result.Data = listaBarnices.Select(barniz => new DetalleBarnizDTO
            {
                Articulo = TinyMapper.Map<AArticuDTO>(barniz)
            }).ToList();
        }

        return result;
    }
}