namespace GestionAPQ_BLZ.MediaTR.Command;

using Common.ResponseModels.ResponseModels;
using DTO.Genericos;
using GestionAQP_BLZ.Server.Data.Entities.APQLitalsa;
using MediatR;
using Nelibur.ObjectMapper;
using Repositories.Base.APQLitalsa;

public record GrabarPesoEnvaseCommand(TablaPesosEnvasesDTO PesoEnvase) : IRequest<SingleResult<int>>;

public class GrabarPesoEnvaseCommandHandler : IRequestHandler<GrabarPesoEnvaseCommand, SingleResult<int>>
{
    private readonly ITablaPesosEnvasesRepo _tablaPesosEnvasesRepo;

    public GrabarPesoEnvaseCommandHandler(ITablaPesosEnvasesRepo tablaPesosEnvasesRepo)
    {
        _tablaPesosEnvasesRepo = tablaPesosEnvasesRepo;
    }

    public async Task<SingleResult<int>> Handle(GrabarPesoEnvaseCommand request, CancellationToken cancellationToken)
    {
        var result = new SingleResult<int>
        {
            Data = 0,
            Errors = []
        };

        TablaPesosEnvases? pesoEnvase;

        // si tiene ID, es una actualización
        if (request.PesoEnvase.Id != 0)
        {
            pesoEnvase = await _tablaPesosEnvasesRepo.GetPesoEnvasePorId(
                request.PesoEnvase.Id,
                false,
                cancellationToken);

            if (pesoEnvase == null)
            {
                result.Errors.Add("No se ha encontrado el peso de envase en la base de datos.");
                return result;
            }

            // actualizamos campos
            pesoEnvase.Numenvase = request.PesoEnvase.Numenvase;
            pesoEnvase.Idproducto = request.PesoEnvase.Idproducto;
            pesoEnvase.Fecha = request.PesoEnvase.Fecha;
            pesoEnvase.FechaCaducidad = request.PesoEnvase.FechaCaducidad;
            pesoEnvase.Pesoteoricoentrada = request.PesoEnvase.Pesoteoricoentrada;
            pesoEnvase.Pesorealentrada = request.PesoEnvase.Pesorealentrada;
            pesoEnvase.Pesovacio = request.PesoEnvase.Pesovacio;
            pesoEnvase.Pesoenvase = request.PesoEnvase.Pesoenvase;
            pesoEnvase.RealizadoPor = request.PesoEnvase.RealizadoPor;
            pesoEnvase.FechaFin = request.PesoEnvase.FechaFin;
            pesoEnvase.Ubicacion = request.PesoEnvase.Ubicacion;
            pesoEnvase.Lote = request.PesoEnvase.Lote;
            pesoEnvase.StockActual = request.PesoEnvase.StockActual;
            pesoEnvase.Observaciones = request.PesoEnvase.Observaciones;
        }
        else
        {
            // es un nuevo peso de envase
            pesoEnvase = TinyMapper.Map<TablaPesosEnvases>(request.PesoEnvase);
            pesoEnvase.Fecha = DateTime.Now;
        }

        var resultDb = await _tablaPesosEnvasesRepo.UpdateEntityWithId(cancellationToken, pesoEnvase);
        result.Data = resultDb;

        return result;
    }
}
