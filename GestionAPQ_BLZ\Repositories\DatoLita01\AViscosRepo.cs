﻿namespace GestionAPQ_BLZ.Repositories.DatoLita01;

using Base;
using Base.DatoLita01;
using GestionAQP_BLZ.Server.Data.DbContexts.DatoLita01;
using GestionAQP_BLZ.Server.Data.Entities.DatoLita01;
using Microsoft.EntityFrameworkCore;

public class AViscosRepo : Repository<AViscos, DatoLita01Context>, IAViscosRepo
{
    public AViscosRepo(DatoLita01Context dbContext) : base(dbContext)
    {
    }

    public IQueryable<AViscos> GetIQueryableViscosidades(string? codigo, bool asNoTracking)
    {
        var query = asNoTracking ? DbSet.AsNoTracking() : DbSet.AsQueryable();
        query = !string.IsNullOrEmpty(codigo) ? query.Where(i => i.Codigo.Equals(codigo)) : query;

        return query;
    }
}