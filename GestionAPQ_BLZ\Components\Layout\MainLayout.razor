﻿@inherits LayoutComponentBase
@inject IConfiguration Configuration

<DxToastProvider FreezeOnClick="true"
                 HorizontalAlignment="HorizontalAlignment.Right"
                 VerticalAlignment="VerticalEdge.Top"
                 ShowCloseButton="true"
                 DisplayTime="@TimeSpan.FromSeconds(3)"
                 SizeMode="SizeMode.Medium"/>
<DxLayoutBreakpoint DeviceSize="DeviceSize.XSmall" IsActive="IsXSmallScreen" IsActiveChanged="IsActiveChanged"/>
<div class="card border-0 vh-100">
	<div class="d-flex justify-content-start text-white navbar-header">
		<div class="contenedor-icono-hamburger px-3 border-end d-flex align-items-center justify-content-center">
			<button class="navbar-toggler" @onclick="ToggleDrawer">
				<span class="navbar-toggler-icon"></span>
			</button>
		</div>

		<div class="px-3 d-flex justify-content-between w-100">
			<div class="fw-bold d-flex align-items-center">
				<NavLink class="nav-link" href="" Match="NavLinkMatch.All">
					<img class="img-fluid" style="max-height: 45px;" src="/images/logo-blanco.png"/>
				</NavLink>
			</div>

			<div class="d-flex align-items-center">
				<NavLink class="nav-link" href="" Match="NavLinkMatch.All">
					<span class="fw-bold" style="font-size: 18px;">@(Configuration["AppMetadata:ProjectNameDisplay"])</span>
				</NavLink>
			</div>
		</div>
	</div>

	<DxDrawer @bind-IsOpen="IsOpen"
	          Mode="Mode"
	          PanelWidth="315px"
	          MiniModeEnabled="true"
	          MiniPanelWidth="65px"
	          ApplyBackgroundShading="true">
		<BodyTemplate>
			<div class="flex-column h-100 w-100 overflow-hidden">
				<div class="nav-item">
					<NavLink class="nav-link mx-2" href="" Match="NavLinkMatch.All"
					         title="Inicio">
						<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
							<span class="bi bi-house icono-sidebar"></span>
						</div>
						<span class="ms-3 text-nowrap">Inicio</span>
					</NavLink>
				</div>
				<div class="nav-item">
					<NavLink class="nav-link mx-2" href="ProductosPorNodrizas" title="Productos Por Nodrizas">
						<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
							<span class="bi bi-archive-fill icono-sidebar"></span>
						</div>
						<span class="ms-3 text-nowrap">Productos Por Nodrizas</span>
					</NavLink>
				</div>
				<div class="nav-item">
					<NavLink class="nav-link mx-2" href="EstadoInspecciones" title="Estado Inspecciones">
						<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
							<span class="bi bi-file-earmark-fill icono-sidebar"></span>
						</div>
						<span class="ms-3 text-nowrap">Estado Inspecciones</span>
					</NavLink>
				</div>
				<div class="nav-item">
					<NavLink class="nav-link mx-2" href="ControlPesosEnvases" title="Control Pesos Envases">
						<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
							<span class="bi bi-speedometer icono-sidebar"></span>
						</div>
						<span class="ms-3 text-nowrap">Control Pesos Envases</span>
					</NavLink>
				</div>
				<div class="nav-item">
					<NavLink class="nav-link mx-2" href="TablasControl" title="Listados">
						<div class="contenedor-icono-sidebar d-flex justify-content-center align-items-center">
							<span class="bi bi-table icono-sidebar"></span>
						</div>
						<span class="ms-3 text-nowrap">Listados</span>
					</NavLink>
				</div>
			</div>
		</BodyTemplate>
		<TargetContent>
			<div id="div-body" class="w-100 h-100 m-0 p-4 overflow-auto d-flex flex-column">
				@Body
			</div>
		</TargetContent>
	</DxDrawer>
</div>

<div id="blazor-error-ui">
	An unhandled error has occurred.
	<a href="" class="reload">Reload</a>
	<a class="dismiss">🗙</a>
</div>

@code {

	// https://docs.devexpress.com/Blazor/DevExpress.Blazor.DxMenu
	// https://demos.devexpress.com/blazor/Drawer#Position
	private bool IsXSmallScreen { get; set; }
	private bool? _isOpen = false; // por defecto cerrado en vista completa
	private bool? _isOpenBigScreen = true; // estado guardado para pantallas grandes

	private bool IsOpen
	{
		get => _isOpen ?? !IsXSmallScreen;
		set => _isOpen = value;
	}

	private DrawerMode Mode => IsXSmallScreen ? DrawerMode.Overlap : DrawerMode.Shrink;

	private void IsActiveChanged(bool isXSmall)
	{
		var previousIsXSmall = IsXSmallScreen;
		IsXSmallScreen = isXSmall;

		if (isXSmall)
		{
			// guardamos el estado actual para cuando vuelva a pantalla grande
			if (!previousIsXSmall)
				_isOpenBigScreen = _isOpen;
			// en pantalla pequeña inicialmente cerrado
			_isOpen = false;
		}
		else
		{
			if (previousIsXSmall && _isOpen == true) // si estaba abierto en pantalla pequeña, lo abrimos en grande
				_isOpen = true;
			else // si no, restauramos el estado guardado previamente
				_isOpen = _isOpenBigScreen;
		}
	}

	private void ToggleDrawer()
	{
		IsOpen = !IsOpen;

		// guardamos el estado para pantallas grandes
		if (!IsXSmallScreen)
			_isOpenBigScreen = IsOpen;
	}

}